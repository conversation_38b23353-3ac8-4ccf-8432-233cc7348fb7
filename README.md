# TOUV LLM Framework XML

A LLM framework for XML-based text refinement and processing, now powered by uv and litellm.

## Recent Migration

This project has been migrated from pip to uv for dependency management and from OpenAI to litellm for LLM API calls.

### Changes Made

- **Dependency Management**: Replaced `requirements.txt` with `pyproject.toml` and uv
- **LLM Provider**: Replaced direct OpenAI client with litellm for multi-provider support
- **Build Scripts**: Created uv-compatible batch scripts for Windows

## Setup

### Prerequisites

- Python 3.9 or higher
- uv (will be installed automatically if not present)

### Installation

1. Run the setup script:
   ```bash
   setup.bat
   ```

   This will:
   - Install uv if not present
   - Sync all dependencies
   - Install the package in development mode

2. Create a `.env` file with your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

## Usage

### Running the Application

```bash
run.bat
```

Or directly with uv:
```bash
uv run python src/main.py
```

### Upgrading Dependencies

```bash
packages_upgrade.bat
```

## Features

- Multi-stage text refinement using XML-based prompts
- Support for multiple LLM providers through litellm
- Configurable processing stages and perspectives
- Automatic output generation and history tracking

## Project Structure

- `src/` - Main application code
- `src/prompts/` - XML prompt templates
- `src/subjective_outputs/` - Generated output files
- `src/history/` - Processing history and logs
- `pyproject.toml` - Project configuration and dependencies
- `uv.lock` - Locked dependency versions

## Dependencies

Key dependencies managed through uv:
- `litellm` - Multi-provider LLM API abstraction
- `python-dotenv` - Environment variable management
- `pydantic` - Data validation
- `httpx` - HTTP client for API calls
