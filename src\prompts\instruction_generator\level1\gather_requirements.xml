<prompt>
  <!--
      Purpose (Why):
      Collect initial user/system requirements and constraints for building LLM instructions.
  -->
  <purpose>Gather core requirements for the LLM instructions.</purpose>

  <instructions>
    <!-- "How" we refine: gather major needs and constraints -->
    <instruction>You are collecting the main objectives and constraints from [SCENARIO_CONTEXT].</instruction>
    <instruction>Focus on high-level goals, must-have features, and any critical boundaries.</instruction>
    <instruction>List them in concise bullet points or short sentences.</instruction>
  </instructions>

  <!-- "What" is refined: the raw content or scenario details -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
