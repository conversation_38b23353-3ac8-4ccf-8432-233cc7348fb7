  {
  "initial_prompt": "please rephrase this into the perspective of a fly",
  "sequence_id": "1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "please rephrase this into the perspective of a fly",
      "responses": {
          "gpt-5-chat-latest": {
          "model": "gpt-5-chat-latest",
          "content": "{\"raw_prompt\":\"please rephrase this into the perspective of a fly\",\"role\":\"explosive_decomposer\",\"input\":{\"raw_prompt\":\"please rephrase this into the perspective of a fly\"},\"process\":[\"identify_explicit_asks\",\"surface_hidden_assumptions\",\"enumerate_sub_goals\",\"reveal_blockers\"],\"constraints\":[\"no_solutions\",\"pure_decomposition_only\"],\"requirements\":[\"complete_goal_map\"],\"output\":{\"goal_map\":[]}}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"please rephrase this into the perspective of a fly\\\"```'\\n\\n\",\n  \"raw_prompt\": \"please rephrase this into the perspective of a fly\",\n  \"role\": \"explosive_decomposer\",\n  \"input\": {\n    \"raw_prompt\": \"please rephrase this into the perspective of a fly\"\n  },\n  \"process\": [\n    \"identify_explicit_asks\",\n    \"surface_hidden_assumptions\",\n    \"enumerate_sub_goals\",\n    \"reveal_blockers\"\n  ],\n  \"constraints\": [\n    \"no_solutions\",\n    \"pure_decomposition_only\"\n  ],\n  \"requirements\": [\n    \"complete_goal_map\"\n  ],\n  \"output\": {\n    \"goal_map\": []\n  }\n}",
      "responses": {
          "gpt-5-chat-latest": {
          "model": "gpt-5-chat-latest",
          "content": "{\n\t\"instruction_format\": \"Identify the explicit request and reveal any hidden assumptions in the prompt. Enumerate the sub-goals involved and identify any potential blockers. Be sure to focus solely on breaking down the task without providing any solutions. Ensure that the goal map is fully completed.\"\n}",
        }
      }
    }