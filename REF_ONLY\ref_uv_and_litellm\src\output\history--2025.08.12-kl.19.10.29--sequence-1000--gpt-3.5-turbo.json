  {
  "initial_prompt": "Transform this into a clear directive.",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Transform this into a clear directive.",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"role\": \"instruction_converter\",\n\t\"input\": \"Transform this into a clear directive.\",\n\t\"process\": [\"strip_first_person_references()\", \"convert_statements_to_directives()\", \"identify_key_actions()\", \"transform_declaratives_to_imperatives()\", \"maintain_procedural_structure()\", \"preserve_technical_terminology()\", \"retain_sequential_flow()\", \"maintain_contextual_integrity()\"],\n\t\"constraints\": [\"deliver_clear_actionable_commands()\", \"preserve_original_sequence()\", \"maintain_domain_specificity()\"],\n\t\"requirements\": [\"remove_self_references()\", \"use_command_voice()\", \"preserve_technical_accuracy()\", \"maintain_original_intent()\"],\n\t\"output\": {\n\t\t\"instruction_format\": \"Clarify the directive provided in the input.\"\n\t}\n}",
        }
      }
    }