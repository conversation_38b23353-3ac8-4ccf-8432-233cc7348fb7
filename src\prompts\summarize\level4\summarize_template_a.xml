<prompt>
  <!--
      Purpose (Why is it being refined?):
      Summarize content by referencing all relevant scenario parameters (from a central manifest).
  -->
  <purpose>Summarize the content according to [SCENARIO_TYPE] and related scenario parameters.</purpose>

  <!--
      Instructions (How is it being refined?):
      1. Incorporate [PERSPECTIVE_INSTRUCTIONS], [INSTRUCTIONS], and references to [TONE], [AUDIENCE].
      2. Keep the summary cohesive and scenario-aligned.
  -->
  <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
  <instructions>
    [INSTRUCTIONS]
  </instructions>
  <format>
    [FORMAT]
  </format>

  <!--
      Example Output (Optional blueprint):
      Demonstrates how the final template can look in practice.
  -->
  <example_output>
    [EXAMPLE_OUTPUT]
  </example_output>

  <!--
      Content (What is being refined?):
      The text or final-level summary to be processed.
  -->
  <content>
    [CONTENT]
  </content>
</prompt>
