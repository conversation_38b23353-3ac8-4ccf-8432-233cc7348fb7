  {
  "initial_prompt": "Create a template that analyzes execution performance data and suggests optimizations for async LLM orchestration systems. The template should identify bottlenecks in streaming responses, catalog loading, sequence resolution, and model switching. It should output specific optimization recommendations with measurable performance targets.",
  "sequence_id": "1700",
  "results": [
      {
      "instruction": "# Universal Template Constructor\n\nYour goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:\n\n`{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`",
      "step": "a",
      "title": "Universal Template Constructor",
      "input": "Create a template that analyzes execution performance data and suggests optimizations for async LLM orchestration systems. The template should identify bottlenecks in streaming responses, catalog loading, sequence resolution, and model switching. It should output specific optimization recommendations with measurable performance targets.",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"template\": {\n\t\t\"title\": \"Async LLM Orchestration Performance Optimization Template\",\n\t\t\"interpretation\": \"This template aims to analyze execution performance data of async LLM orchestration systems. It focuses on identifying bottlenecks in streaming responses, catalog loading, sequence resolution, and model switching to suggest specific optimization recommendations with measurable performance targets.\",\n\t\t\"transformation\": \"1. Analyze the execution performance data of the async LLM orchestration system.\\n2. Identify bottlenecks related to streaming responses, catalog loading, sequence resolution, and model switching.\\n3. Suggest optimization recommendations for each bottleneck identified.\\n4. Specify measurable performance targets for each optimization recommendation.\",\n\t\t\"context\": {\n\t\t\t\"specific_areas\": [\"streaming responses\", \"catalog loading\", \"sequence resolution\", \"model switching\"],\n\t\t\t\"optimization_recommendations\": \"Specific optimization recommendations with measurable performance targets will be provided.\",\n\t\t\t\"performance_targets\": \"Measurable performance targets for each optimization recommendation will be defined.\"\n\t\t}\n\t}\n}",
        }
      }
    }