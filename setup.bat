@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: TOUV LLM Framework XML - Setup Script
:: Replaces py_venv_init.bat with uv-based dependency management
:: =============================================================================

echo.
echo ========================================
echo TOUV LLM Framework XML - Setup
echo ========================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check if uv is installed
where uv >nul 2>&1
if errorlevel 1 (
    echo Error: uv is not installed or not in PATH
    echo.
    echo Please install uv first:
    echo https://docs.astral.sh/uv/getting-started/installation/
    echo.
    echo For Windows, you can use:
    echo   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    echo.
    pause
    exit /b 1
)

echo ✓ uv is installed
echo.

:: Initialize project with uv
echo Initializing project with uv...
uv sync
if errorlevel 1 (
    echo Error: Failed to sync dependencies
    pause
    exit /b 1
)

echo ✓ Dependencies synced successfully
echo.

:: Install package in development mode
echo Installing package in development mode...
uv pip install -e .
if errorlevel 1 (
    echo Warning: Failed to install package in development mode
    echo This may affect some functionality
)

echo ✓ Package installed in development mode
echo.

:: Check for .env file
if not exist ".env" (
    echo.
    echo ⚠️  Warning: .env file not found
    echo Please create a .env file with your API keys:
    echo   OPENAI_API_KEY=your_openai_api_key_here
    echo.
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo You can now run the application with:
echo   run.bat
echo.
echo Or upgrade packages with:
echo   packages_upgrade.bat
echo.
pause
