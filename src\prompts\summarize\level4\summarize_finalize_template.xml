<prompt>
    <!--
        Purpose (Why):
        Summarize the content according to the final scenario parameters.
    -->
    <purpose>Summarize the content according to the given scenario parameters.</purpose>

    <!--
        Instructions (How):
        Possibly referencing scenario manifest placeholders for instructions, format, etc.
    -->
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>

    <instructions>
        [INSTRUCTIONS]
    </instructions>
    <format>
        [FORMAT]
    </format>
    <example_output>
        [EXAMPLE_OUTPUT]
    </example_output>

    <!--
        Content (What is being refined?):
        The final text content that needs specialized output for the scenario.
    -->
    <content>
        [CONTENT]
    </content>
</prompt>
