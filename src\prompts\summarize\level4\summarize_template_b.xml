<prompt>
    <purpose>Summarize the content according to the given scenario parameters.</purpose>

    <!-- "Why" we refine: ensures the final perspective alignment -->
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>

    <!-- "How" we refine: references scenario manifest for instructions & format -->
    <instructions>
        [INSTRUCTIONS]
    </instructions>
    <format>
        [FORMAT]
    </format>
    <example_output>
        [EXAMPLE_OUTPUT]
    </example_output>

    <!-- "TL;DR" injection -->
    <instruction>Finally, provide a 1-2 sentence TL;DR labeled as "TL;DR:".</instruction>

    <!-- "What" is refined: the final text content that needs specialized output -->
    <content>
        [CONTENT]
    </content>
</prompt>
