  {
  "initial_prompt": "Test the new prompt system",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Test the new prompt system",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"instruction_format\": \"Strip any first-person references. Convert the statements to directives. Identify key actions. Transform declarative sentences to imperatives. Maintain the procedural structure. Preserve technical terminology. Retain the sequential flow. Maintain the contextual integrity.\"\n}",
        }
      }
    }