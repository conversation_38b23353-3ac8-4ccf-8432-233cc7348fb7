import sys, os, json, logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv
import litellm

# ==============================
# Static Configuration
# ==============================
MODEL_REGISTRY = {
    # OpenAI
    "gpt-3.5-turbo": "gpt-3.5-turbo",
    "gpt-3.5-turbo-1106": "gpt-3.5-turbo-1106",
    "gpt-4": "gpt-4",
    "gpt-4-0125-preview": "gpt-4-0125-preview",
    "gpt-4-0613": "gpt-4-0613",
    "gpt-4-1106-preview": "gpt-4-1106-preview",
    "gpt-4-turbo": "gpt-4-turbo",
    "gpt-4-turbo-2024-04-09": "gpt-4-turbo-2024-04-09",
    "gpt-4-turbo-preview": "gpt-4-turbo-preview",
    "gpt-4o": "gpt-4o",
    "gpt-4o-mini": "gpt-4o-mini",
    # Anthropic
    "claude-3-opus": "anthropic/claude-3-opus-20240229",
    "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
    "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
    # Google
    "gemini-pro": "gemini/gemini-1.5-pro",
    "gemini-flash": "gemini/gemini-1.5-flash-latest",
}

PERSPECTIVE_INSTRUCTIONS = {
    "descriptive": "Summarize, describing the speaker in the third person.",
    "first-person": "Summarize in the first person, as if you are the speaker.",
    "second-person": "Summarize using the second person, directly addressing the audience.",
    "collective-first-person": "Summarize using 'we' or 'our', reflecting a group perspective.",
    "scientific-third-person-objective": "Summarize objectively, focusing on data and facts.",
    "scientific-third-person-analytical": "Summarize analytically, using precise and technical language.",
    "narrative": "Summarize as a story, highlighting the key events.",
    "character-persona": "Summarize from a specific character's perspective, adopting their tone and vocabulary.",
    "perceiver-perspective": "Summarize as an astute observer, focusing on the underlying motivations and emotions driving the speaker's actions.",
    "default": "Provide a neutral summary, describing the speaker in the third person."
}

DEFAULT_PARAMS = {
    "model": "gpt-3.5-turbo-1106",
    "temperature": 0.7,
    "max_tokens": 800
}
PLACEHOLDERS = {
    "content": "[PASTE_TEXT_HERE]",
    "keywords": "[KEYWORDS_HERE]",
    "scenario_type": "[SCENARIO_TYPE]",
    "scenario_context": "[SCENARIO_CONTEXT]",
    "tone": "[TONE]",
    "audience": "[AUDIENCE]"
}

# ==============================
# App Class
# ==============================
class RefinementApp:

    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

    def __init__(self):
        load_dotenv()
        self._configure_io_encoding()
        self._configure_litellm()
        self.script_dir = Path(__file__).resolve().parent
        self.logs_dir = self.script_dir / 'logs'
        self.logs_dir.mkdir(exist_ok=True)
        self._setup_logging()
        self.output_index = 1

    def _configure_io_encoding(self):
        for stream in [sys.stdout, sys.stderr]:
            if hasattr(stream, "reconfigure"):
                stream.reconfigure(encoding="utf-8", errors="replace")

    def _configure_litellm(self):
        """Configure LiteLLM settings."""
        # Basic LiteLLM configuration
        litellm.set_verbose = False
        litellm.num_retries = 2
        litellm.request_timeout = 30

        # Ensure API key is available
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise EnvironmentError("OPENAI_API_KEY not set.")

        # Set the API key for OpenAI
        os.environ["OPENAI_API_KEY"] = api_key

    def _setup_logging(self):
        logging.basicConfig(
            filename=self.logs_dir / 'refinement.log',
            level=logging.INFO,
            format=self.LOG_FORMAT,
            encoding='utf-8'
        )

    def _load_json(self, filepath: Path) -> Dict:
        try:
            return json.loads(filepath.read_text(encoding='utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            logging.error(f"Error loading JSON from {filepath}: {e}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error loading JSON from {filepath}: {e}")
            raise

    def _read_file(self, filepath: Path) -> str:
        try:
            return filepath.read_text(encoding='utf-8').strip()
        except (UnicodeDecodeError, FileNotFoundError) as e:
            logging.error(f"Error reading file {filepath}: {e}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while reading {filepath}: {e}")
            raise

    def _load_prompt(self, filepath: Path) -> str:
        try:
            return filepath.read_text(encoding='utf-8')
        except UnicodeDecodeError as e:
            logging.error(f"Encoding error reading prompt file {filepath}: {e}")
            return ""
        except FileNotFoundError:
            logging.error(f"Prompt file not found: {filepath}")
            return ""
        except Exception as e:
            logging.error(f"Unexpected error while loading {filepath}: {e}")
            return ""

    def _append_history(self, entry: Dict, history_path: Path):
        history = []
        if history_path.exists():
            try:
                history = self._load_json(history_path)
            except json.JSONDecodeError:
                logging.warning(f"History file {history_path} is corrupted. Starting fresh.")
            except Exception as e:
                logging.error(f"Unexpected error while loading {history_path}: {e}")
        history.append(entry)
        try:
            history_path.write_text(json.dumps(history, indent=4, ensure_ascii=False), encoding='utf-8')
        except Exception as e:
            logging.error(f"Error writing to history file {history_path}: {e}")

    def _resolve_prompts(self, prompt_entries: List[Dict], base_dir: Path) -> List[Dict]:
        resolved = []
        for entry in prompt_entries:
            path = entry.get('path')
            if path:
                abs_path = base_dir / path if not Path(path).is_absolute() else Path(path)
                if abs_path.exists():
                    entry['path'] = abs_path
                    resolved.append(entry)
                else:
                    logging.error(f"Prompt file not found: {abs_path}")
            else:
                logging.error("No path specified for a prompt entry.")
        return resolved

    def _parse_level(self, prompt_path: Path) -> str:
        for part in reversed(prompt_path.parts):
            if part.lower().startswith('level'):
                return part
        return 'unknown_level'

    def _update_lineage(self, level: str, changes: str, reasoning: str):
        lineage_path = self.script_dir / 'history' / 'lineage.json'
        lineage_path.parent.mkdir(exist_ok=True)
        lineage_data = []
        if lineage_path.exists():
            try:
                lineage_data = self._load_json(lineage_path)
            except Exception as e:
                logging.warning(f"Could not read lineage file: {e}")
        lineage_data.append({
            "level_name": level,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "changes": changes,
            "reasoning_snippet": reasoning
        })
        try:
            lineage_path.write_text(json.dumps(lineage_data, indent=4, ensure_ascii=False), encoding='utf-8')
        except Exception as e:
            logging.error(f"Could not write to lineage file {lineage_path}: {e}")

    def _run_llm(self, prompt: str, model: str, temperature: float, max_tokens: int) -> Optional[str]:
        try:
            print(f'prompt: {prompt}')

            # Get the actual model ID from registry
            actual_model = MODEL_REGISTRY.get(model, model)

            response = litellm.completion(
                model=actual_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Error during LLM call: {e}")
            return None

    def _build_perspective_instructions(self, mode: str) -> str:
        return PERSPECTIVE_INSTRUCTIONS.get(mode.lower(), PERSPECTIVE_INSTRUCTIONS["default"])

    def _format_prompt_content(self, template: str, current_response: str, keywords: Optional[str], config: Dict, placeholders: Dict) -> str:
        scenario_type = config.get("scenario_type", "")
        scenario_context = config.get("scenario_context", "")
        tone = config.get("tone", "neutral")
        audience = config.get("audience", "general audience")
        instructions = config.get("instructions", [])
        format_sections = config.get("format", {}).get("sections", [])
        example_output_data = config.get("example_output", {})
        perspective_mode = config.get("perspective_mode", "descriptive")
        perspective_instructions = self._build_perspective_instructions(perspective_mode)
        instructions_str = "\n".join(f"- {instr}" for instr in instructions)
        format_str = "Sections:\n" + "\n".join(f"- {sec}" for sec in format_sections) if format_sections else "No specific format required."
        ex_title = example_output_data.get("title", "Example Title")
        ex_sections = example_output_data.get("sections", {})
        ex_output_str = f"**Title:** {ex_title}\n\n" + "\n\n".join(f"**{sec_name}:**\n{sec_content}" for sec_name, sec_content in ex_sections.items())
        prompt_content = template.replace(placeholders.get("content", "[PASTE_TEXT_HERE]"), current_response.strip())
        prompt_content = prompt_content.replace(placeholders.get("scenario_type", "[SCENARIO_TYPE]"), scenario_type)
        prompt_content = prompt_content.replace(placeholders.get("scenario_context", "[SCENARIO_CONTEXT]"), scenario_context)
        prompt_content = prompt_content.replace(placeholders.get("tone", "[TONE]"), tone)
        prompt_content = prompt_content.replace(placeholders.get("audience", "[AUDIENCE]"), audience)

        if keywords and placeholders.get("keywords", "[KEYWORDS_HERE]") in prompt_content:
            prompt_content = prompt_content.replace(placeholders.get("keywords", "[KEYWORDS_HERE]"), keywords)

        if "[INSTRUCTIONS]" in prompt_content:
            prompt_content = prompt_content.replace("[INSTRUCTIONS]", instructions_str)
        if "[FORMAT]" in prompt_content:
            prompt_content = prompt_content.replace("[FORMAT]", format_str)
        if "[EXAMPLE_OUTPUT]" in prompt_content:
            prompt_content = prompt_content.replace("[EXAMPLE_OUTPUT]", ex_output_str.strip())

        prompt_content = prompt_content.replace("[PERSPECTIVE_INSTRUCTIONS]", perspective_instructions)

        return prompt_content

    def _apply_prompt(self,
                     prompt_path: Path,
                     current_response: str,
                     keywords: Optional[str],
                     default_params: Dict,
                     override_params: Dict,
                     placeholders: Dict,
                     config: Dict) -> Optional[str]:
        template = self._load_prompt(prompt_path)
        if not template:
            return None

        prompt_content = self._format_prompt_content(template, current_response, keywords, config, placeholders)
        model = override_params.get("model", default_params.get("model", "gpt-3.5-turbo-1106"))
        temperature = override_params.get("temperature", default_params.get("temperature", 0.7))
        max_tokens = override_params.get("max_tokens", default_params.get("max_tokens", 800))

        if model not in VALID_MODELS:
            logging.warning(f"Model '{model}' invalid. Using default.")
            model = default_params.get("model", "gpt-3.5-turbo-1106")

        refined_response = self._run_llm(prompt_content, model, temperature, max_tokens)
        if refined_response:
            agent_name = prompt_path.stem
            logging.info(f"Prompt '{agent_name}' applied successfully.")
            return refined_response
        else:
            agent_name = prompt_path.stem
            logging.warning(f"Prompt '{agent_name}' failed to return a response.")
            return None

    def _assess_quality(self, response: str, indicators: List[str], min_count: int) -> bool:
        return sum(indicator in response.lower() for indicator in indicators) >= min_count

    def _should_run_stage(self, stage: Dict, config: Dict, previous_output: Optional[str]) -> bool:
        return stage.get("run_if", "true").lower() == "true"

    def _run_stage(
        self,
        stage: Dict,
        inputs: Dict,
        config: Dict,
        history_path: Path,
        script_name: str,
        previous_output: Optional[str]
    ) -> (Optional[Dict], Optional[str]):

        if not self._should_run_stage(stage, config, previous_output):
            return None, previous_output

        keywords = config.get("keywords")
        prompts_sequence = stage.get("prompts_sequence", [])
        prompts = self._resolve_prompts(prompts_sequence, self.script_dir)
        if not prompts:
            logging.error(f"No valid prompts in stage {stage['name']}")
            return None, previous_output

        input_key = prompts[0].get("input_key", "initial")
        if previous_output is not None:
            current_response = previous_output
        elif input_key in inputs:
            try:
                current_response = self._read_file(self.script_dir / inputs[input_key])
            except Exception:
                return None, previous_output
        else:
            logging.error(f"Input key '{input_key}' not found")
            return None, previous_output

        qc = stage.get("quality_check", {})
        indicators = qc.get("indicators", ["clear", "example", "concise", "relevant"])
        min_count = qc.get("min_count", 2)
        min_prompts = qc.get("min_prompts_before_early_termination", 3)

        history_entry = {
            "script": script_name,
            "stage": stage["name"],
            "datestamp": datetime.now().strftime("%Y.%m.%d"),
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "initial_input": current_response,
            "steps": []
        }

        print(f'- Initial: "{current_response}"\n\n---\n')

        applied_prompts = 0
        for p in prompts:
            override_params = {k: v for k, v in p.items() if k in ["model", "temperature", "max_tokens"]}
            refined = self._apply_prompt(
                prompt_path=p['path'],
                current_response=current_response,
                keywords=keywords,
                default_params=DEFAULT_PARAMS,
                override_params=override_params,
                placeholders=PLACEHOLDERS,
                config=config
            )

            prompt_name = p['path'].stem

            if refined:
                tldr_part = ""
                if prompt_name == "summarize_tldr" and "**TL;DR:**" in refined:
                    _, tldr_part = refined.split("**TL;DR:**", 1)
                    tldr_part = tldr_part.strip()

                step_entry = {
                    "prompt": prompt_name,
                    "output": refined,
                    "tldr_raw": tldr_part
                } if prompt_name == "summarize_tldr" else {
                    "prompt": prompt_name,
                    "output": refined
                }
                history_entry["steps"].append(step_entry)

                print(f'- Prompt: {prompt_name}')
                print(f'- Step {applied_prompts + 1}: "{refined}"\n')

                level_folder = self._parse_level(p['path'])
                self._update_lineage(
                    level=level_folder,
                    changes=f"Applied {prompt_name}",
                    reasoning="Refined output using prompt at this level."
                )
                current_response = refined
            else:
                print(f'- Prompt: {prompt_name}')
                print(f'- Step {applied_prompts + 1}: # Refinement skipped due to failure.\n')

            applied_prompts += 1

            if applied_prompts >= min_prompts and self._assess_quality(current_response, indicators, min_count):
                logging.info(f"Early termination in stage {stage['name']}: quality criteria met.")
                break

        history_entry["final_output"] = current_response
        self._append_history(history_entry, history_path)
        return history_entry, current_response

    def _render_output(self, result: Dict) -> str:
        lines = ["# Single Consolidated Summary\n"]
        for stage_name, stage_data in result.items():
            lines.append("**Step 0**\n")
            lines.append("* initial_input.txt")
            for line in stage_data.get("initial_input", "").splitlines():
                if line_stripped := line.strip():
                    lines.append(f"  + {line_stripped}")
            lines.append("\n---\n")

            for idx, step in enumerate(stage_data.get("steps", []), start=1):
                prompt_name = step.get("prompt", "Unknown Prompt")
                output_text = step.get("output", "")

                lines.append(f"**Step {idx}**\n")
                lines.append(f"* {prompt_name}")

                for text_line in output_text.splitlines():
                    txt_stripped = text_line.strip()
                    if not txt_stripped or txt_stripped.startswith("```"):
                        continue

                    if txt_stripped.startswith("## ") or txt_stripped.startswith("### "):
                        lines.append(f"  + __{txt_stripped.lstrip('# ').strip()}__")
                        continue
                    if txt_stripped.startswith("- **") and txt_stripped.endswith("**:"):
                        try:
                            title_part, text_part = txt_stripped.split("**:", 1)
                            title = title_part.replace("- **", "").strip()
                            text = text_part.strip()
                            lines.append(f"  + __{title}:__ {text}")
                        except ValueError:
                            lines.append(f"  + {txt_stripped.replace('**', '__')}")
                        continue
                    lines.append(f"  + {txt_stripped}")
                lines.append("\n---\n")
            final_output = stage_data.get("final_output", "")
            if final_output:
                lines.append("**Stage's Final Output (Refined):**")
                lines.append(final_output)
                lines.append("")

        lines.append("---\nEnd of Single Consolidated Summary\n")
        return "\n".join(lines)

    def _export_output(self, result: Dict):
        rendered_text = self._render_output(result)
        now = datetime.now()
        date_str = now.strftime("%Y.%m.%d")
        suffix_char = chr(ord('a') + self.output_index - 1)
        filename = f"{date_str}_mySubjectiveOutput_{suffix_char}.md"
        self.output_index += 1
        output_dir = self.script_dir / "subjective_outputs"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / filename
        try:
            output_path.write_text(rendered_text, encoding='utf-8')
            print(f"Subjective output written to: {output_path}")
            logging.info(f"Subjective output written to: {output_path}")
        except Exception as e:
            logging.error(f"Error while writing presentable summaries: {e}")

    def main(self):
        script_name = Path(__file__).name
        # config_path = self.script_dir / 'scenario_config.json'
        config_path = self.script_dir / 'scenario_config_synthesizer.json'
        config = self._load_json(config_path)
        history_path = self.script_dir / 'history' / 'refinement_history.json'
        history_path.parent.mkdir(exist_ok=True)
        inputs = config.get("inputs", {})
        stages = config.get("stages", [])
        final_result = {}
        last_stage_output = None

        for stage in stages:
            stage_history, last_stage_output = self._run_stage(
                stage, inputs, config, history_path, script_name, last_stage_output
            )
            if stage_history:
                final_result[stage["name"]] = stage_history

        print("\nRefinement Process Completed:")
        print(json.dumps(final_result, indent=4, ensure_ascii=False))

        if final_result:
            self._export_output(final_result)
        else:
            print("No final results to export.")

if __name__ == "__main__":
    RefinementApp().main()
