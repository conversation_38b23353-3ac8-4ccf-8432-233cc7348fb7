<prompt>
  <purpose>Summarize the given content within a [SCENARIO_TYPE] context.</purpose>

  <instructions>
    <!-- "Why" we refine: referencing overall scenario -->
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
    <instruction>You are creating a summary intended for [SCENARIO_CONTEXT].</instruction>

    <!-- "How" we refine: ensure concise, neutral language -->
    <instruction>Write 3–5 sentences, focusing on the most essential details.</instruction>
    <instruction>Use Markdown formatting where suitable.</instruction>

    <!-- "TL;DR" injection -->
    <instruction>Then, append a short (1–2 sentence) TL;DR labeled as "TL;DR:".</instruction>
  </instructions>

  <recap>
    <!-- Inserted by code if referencing previous context (optional). -->
  </recap>

  <!-- "What" is refined: the raw content -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
