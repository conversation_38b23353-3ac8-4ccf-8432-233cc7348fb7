[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-systems"
version = "1.0.0"
description = "Multi-step LLM instruction sequence executor with template management"
requires-python = ">=3.9"

dependencies = [
    "litellm>=1.65.5",
    "pydantic>=2.11.3",
    "openai>=1.72.0",
    "aiohttp>=3.11.16",
    "httpx>=0.28.1",
    "tiktoken>=0.9.0",
    "rich>=14.0.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
