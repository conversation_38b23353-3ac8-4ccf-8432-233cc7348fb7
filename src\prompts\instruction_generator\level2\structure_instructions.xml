<prompt>
  <!--
      Purpose (Why):
      Take the gathered requirements and start forming a coherent structure for LLM instructions.
  -->
  <purpose>Organize requirements into a draft outline of actionable instructions.</purpose>

  <instructions>
    <!-- "How" we refine: transform raw requirements into a clear instruction skeleton -->
    <instruction>Review the points from [RECAP] (requirements gathered in Level 1).</instruction>
    <instruction>Create a structured outline of instructions under these sections:
      1) Objective Statement
      2) Prerequisites / Dependencies
      3) Step-by-Step Actions
    </instruction>
    <instruction>Use short, clear bullet points for each section. Prioritize clarity.</instruction>
  </instructions>

  <!-- Recap of previous level’s output -->
  <recap>
    <!-- Inserted by code if referencing previous level -->
  </recap>

  <!-- "What" is refined: the bulleted requirements from Level 1 -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
