{"version": "1.0", "name": "Sequential Execution Batch", "description": "Example showing sequential (non-concurrent) execution for resource-limited environments", "defaults": {"chain_mode": true, "show_inputs": false, "show_system_instructions": false, "show_responses": true, "minified_output": true}, "concurrent": false, "jobs": [{"name": "First Analysis", "prompt": "Analyze the economic impact of remote work", "sequence": "1000", "models": ["gpt-4.1"]}, {"name": "Second Analysis", "prompt": "Examine the social implications of remote work", "sequence": "1000", "models": ["gpt-4.1"]}, {"name": "Third Analysis", "prompt": "Evaluate the technological requirements for remote work", "sequence": "1000", "models": ["gpt-4.1"]}]}