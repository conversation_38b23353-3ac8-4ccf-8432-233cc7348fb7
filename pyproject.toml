[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "touv-llm-framework-xml"
version = "1.0.0"
description = "LLM framework for XML-based text refinement and processing"
requires-python = ">=3.9"

dependencies = [
    "litellm>=1.65.5",
    "python-dotenv>=1.0.1",
    "pydantic>=2.10.4",
    "httpx>=0.28.1",
    "tiktoken>=0.9.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
