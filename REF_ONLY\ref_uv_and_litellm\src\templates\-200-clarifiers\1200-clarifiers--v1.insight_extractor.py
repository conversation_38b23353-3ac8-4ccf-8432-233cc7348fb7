#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "1200-a-insight_extractor": {
        "title": "Insight Extractor",
        "interpretation": "Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor:",
        "transformation": "`{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}`",
        "context": {
            "primal_lever": "Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.",
            "genesis_origin": "Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.",
            "remix_instructions": "Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories."
        }
    }
}




def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



