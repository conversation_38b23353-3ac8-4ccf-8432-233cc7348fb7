<prompt>
  <!--
      Purpose (Why is it being refined?):
      Capture the essential meaning of the content within the context defined by [SCENARIO_TYPE].
      Align the summary with the overarching scenario goals from [SCENARIO_CONTEXT].
  -->
  <purpose>Distill the critical message of the given content for the [SCENARIO_TYPE] scenario.</purpose>

  <!--
      Instructions (How is it being refined?):
      1. Prioritize the "What": Focus on the raw content and strip out irrelevant details.
      2. Centralize scenario intent: [PERSPECTIVE_INSTRUCTIONS], [SCENARIO_CONTEXT].
      3. Ensure the final output is concise, neutral, and uses Markdown if needed.
  -->
  <instructions>
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
    <instruction>Consolidate the most critical points relevant to [SCENARIO_CONTEXT].</instruction>
    <instruction>Limit the summary to 3–5 sentences; keep the tone neutral.</instruction>
    <instruction>Use Markdown formatting if appropriate.</instruction>
  </instructions>

  <recap>
    <!-- Optional: Recap of any prior context if needed -->
  </recap>

  <!--
      Content (What is being refined?):
      The original text to be summarized.
  -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>

