#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "1400-a-value_maximizing_pattern": {
        "title": "Value-Maximizing Instruction Converter",
        "interpretation": "Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter:",
        "transformation": "`{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`",
        # "context": {}
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



