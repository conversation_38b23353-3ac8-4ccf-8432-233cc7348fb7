  {
  "initial_prompt": "Create a framework for microservices testing",
  "sequence_id": "1000|1300",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Create a framework for microservices testing",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"instruction_format\": \"Strip first person references. Convert statements to directives. Identify key actions. Transform declaratives to imperatives. Maintain procedural structure. Preserve technical terminology. Retain sequential flow. Maintain contextual integrity. Deliver clear actionable commands. Preserve original sequence. Maintain domain specificity. Remove self references. Use command voice. Preserve technical accuracy. Maintain original intent.\",\n  \"input\": \"Create a framework for microservices testing\",\n  \"process\": [\"strip_first_person_references()\", \"convert_statements_to_directives()\", \"identify_key_actions()\", \"transform_declaratives_to_imperatives()\", \"maintain_procedural_structure()\", \"preserve_technical_terminology()\", \"retain_sequential_flow()\", \"maintain_contextual_integrity()\"],\n  \"constraints\": [\"deliver_clear_actionable_commands()\", \"preserve_original_sequence()\", \"maintain_domain_specificity()\"],\n  \"requirements\": [\"remove_self_references()\", \"use_command_voice()\", \"preserve_technical_accuracy()\", \"maintain_original_intent()\"]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Enhancer\n\nYour goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:\n\n`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`\n\n## Context Data\n\n```json\n{\n  \"enhancement_principles\": {\n    \"maximal_generality\": \"The instruction must be applicable to the widest possible range of inputs, topics, and contexts.\",\n    \"ruthless_conciseness\": \"Eliminate every superfluous word; every word must serve to increase clarity or impact.\",\n    \"undeniable_actionability\": \"The instruction must be a clear, unambiguous directive that compels specific action.\",\n    \"conceptual_distillation\": \"Boil down complex ideas into their simplest, most potent form.\"\n  },\n  \"success_metrics\": {\n    \"reduced_token_count\": \"A shorter instruction that retains or increases semantic density.\",\n    \"increased_applicability\": \"The instruction is now more widely usable without modification.\",\n    \"sharpened_directive\": \"The instruction's core command is more direct and forceful.\",\n    \"eliminated_ambiguity\": \"No room for misinterpretation of the instruction's purpose.\"\n  }\n}\n```",
      "step": "a",
      "title": "Instruction Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Create a framework for microservices testing\\\"```'\\n\\n\",\n  \"instruction_format\": \"Strip first person references. Convert statements to directives. Identify key actions. Transform declaratives to imperatives. Maintain procedural structure. Preserve technical terminology. Retain sequential flow. Maintain contextual integrity. Deliver clear actionable commands. Preserve original sequence. Maintain domain specificity. Remove self references. Use command voice. Preserve technical accuracy. Maintain original intent.\",\n  \"input\": \"Create a framework for microservices testing\",\n  \"process\": [\n    \"strip_first_person_references()\",\n    \"convert_statements_to_directives()\",\n    \"identify_key_actions()\",\n    \"transform_declaratives_to_imperatives()\",\n    \"maintain_procedural_structure()\",\n    \"preserve_technical_terminology()\",\n    \"retain_sequential_flow()\",\n    \"maintain_contextual_integrity()\"\n  ],\n  \"constraints\": [\n    \"deliver_clear_actionable_commands()\",\n    \"preserve_original_sequence()\",\n    \"maintain_domain_specificity()\"\n  ],\n  \"requirements\": [\n    \"remove_self_references()\",\n    \"use_command_voice()\",\n    \"preserve_technical_accuracy()\",\n    \"maintain_original_intent()\"\n  ]\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"enhanced_instruction_format\": \"Create a microservices testing framework\"\n}",
        }
      }
    }