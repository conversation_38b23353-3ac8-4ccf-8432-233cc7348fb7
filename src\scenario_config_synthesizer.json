{"perspective_mode": "perceiver-perspective", "scenario_type": "In-Depth Multi-Step Synthesis", "scenario_context": "Used to create a systematically refined text, stage by stage, applying multiple viewpoints and checks.", "tone": "neutral", "audience": "decision-makers", "instructions": ["Ensure continuity by referencing the previous output (RECAP) at each stage.", "Maintain a consistent, coherent style across all steps."], "format": {"sections": ["High-Level Summary", "Modular Insights", "Relational Context Notes", "Iterative Refinement Steps"]}, "example_output": {"title": "Synthesized Overview of Dataset", "sections": {"High-Level Summary": "This dataset represents key trends and relationships within [DATA DOMAIN], highlighting [CORE THEMES] and their interdependencies.", "Modular Insights": "- Insight 1: [...]\n- Insight 2: [...]\n- Insight 3: [...]", "Relational Context Notes": "Each insight is connected to the broader narrative of [DATA CONTEXT], ensuring the preservation of interrelationships.", "Iterative Refinement Steps": "- Step 1: Extracted initial insights.\n- Step 2: Structured insights hierarchically.\n- Step 3: Refined modular segments for clarity and cohesion."}}, "inputs": {"initial": "initial_input.txt"}, "keywords": "modular insights, dimensional embedding, relational depth, multistep synthesis chain, in-depth context, varied roles", "placeholders": {"content": "[PASTE_TEXT_HERE]", "keywords": "[KEYWORDS_HERE]", "scenario_type": "[SCENARIO_TYPE]", "scenario_context": "[SCENARIO_CONTEXT]", "tone": "[TONE]", "audience": "[AUDIENCE]"}, "default_parameters": {"model": "gpt-4-0125-preview", "temperature": 0.7, "max_tokens": 800}, "stages": [{"name": "main_summarization", "prompts_sequence": [{"path": "prompts/synthesizer/synthesizer_lvl01_objective_setter.xml", "input_key": "initial"}, {"path": "prompts/synthesizer/synthesizer_lvl02_context_provider.xml"}, {"path": "prompts/synthesizer/synthesizer_lvl03_structure_architect.xml"}, {"path": "prompts/synthesizer/synthesizer_lvl04_example_generator.xml"}], "quality_check": {"indicators": ["role", "dimensional embedding", "modular insights"], "min_count": 2, "min_prompts_before_early_termination": 4}, "run_if": "true"}]}