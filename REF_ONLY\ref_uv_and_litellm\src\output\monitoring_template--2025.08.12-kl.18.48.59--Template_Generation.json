  {
  "initial_prompt": "Create a template for real-time system monitoring that tracks execution metrics, identifies performance bottlenecks, and suggests dynamic optimizations during runtime. The template should output monitoring dashboards with actionable insights.",
  "sequence_id": "1700",
  "results": [
      {
      "instruction": "# Universal Template Constructor\n\nYour goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:\n\n`{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`",
      "step": "a",
      "title": "Universal Template Constructor",
      "input": "Create a template for real-time system monitoring that tracks execution metrics, identifies performance bottlenecks, and suggests dynamic optimizations during runtime. The template should output monitoring dashboards with actionable insights.",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"template\": {\n\t\t\"title\": \"Real-Time System Monitoring for Performance Optimization\",\n\t\t\"interpretation\": \"This template is designed to create a real-time system monitoring solution that tracks execution metrics, identifies performance bottlenecks, and suggests dynamic optimizations during runtime. The focus is on generating monitoring dashboards that provide actionable insights to improve system performance.\",\n\t\t\"transformation\": \"1. Normalize input for system and metric specifications.\\n2. Extract explicit requests for real-time monitoring and optimization features.\\n3. Surface hidden assumptions regarding data collection and visualization requirements.\\n4. Isolate core transformation processes for metric analysis and bottleneck detection.\\n5. Define the role names for different monitoring components (e.g., data collectors, analyzers, visualizers).\\n6. Type inputs and outputs for each component to ensure consistency and compatibility.\\n7. Design atomic process steps for data collection, analysis, and visualization.\\n8. Derive constraints from potential blockers (e.g., resource limitations, data inconsistency).\\n9. Specify requirements for dynamic optimization suggestions and actionable insights.\\n10. Compose the interpretation of goal negation to handle unexpected system behavior.\\n11. Assemble the transformation block for real-time monitoring system creation.\\n12. Compute invariants such as causal link preservation and schema stability.\\n13. Set measurables to track the effectiveness of the monitoring solution.\\n14. Add validation checks to ensure the accuracy and reliability of the monitoring dashboards.\",\n\t\t\"context\": {\n\t\t\t\"goal\": \"To develop a real-time system monitoring solution for performance optimization with dynamic optimizations and actionable insights.\",\n\t\t\t\"constraints\": \"The template is limited to a maximum of 14 operational steps and 520 optional tokens.\",\n\t\t\t\"resources\": \"Must have access to real-time system data and metrics for monitoring purposes.\",\n\t\t\t\"skills\": \"Requires knowledge of system performance metrics, bottleneck identification, and dynamic optimization techniques.\",\n\t\t\t\"output\": \"The template will produce a detailed plan for creating a real-time system monitoring solution with monitoring dashboards.\"\n\t\t}\n\t}\n}",
        }
      }
    }