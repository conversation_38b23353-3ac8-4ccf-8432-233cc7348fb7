<prompt>
  <purpose>Refine the summary within the [SCENARIO_TYPE] setting.</purpose>

  <instructions>
    <!-- "Why" we refine: referencing overall scenario -->
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
    <instruction>Enhance the previously generated summary. See [RECAP] below for context.</instruction>
    <instruction>Keep in mind [SCENARIO_CONTEXT].</instruction>

    <!-- "How" we refine: structured output -->
    <instruction>Output in Markdown format.</instruction>
    <instruction>Divide the summary into 4 sections:
      1. High Level Summary
      2. Main Points
      3. Sentiment
      4. 3 Hot Takes (biased toward the author) + 3 Hot Takes (biased against).
    </instruction>

    <!-- "TL;DR" injection -->
    <instruction>At the end, provide a short TL;DR labeled as "TL;DR:".</instruction>
  </instructions>

  <recap>
    <!-- Inserted by code: brief recap of Level 1 output -->
  </recap>

  <!-- "What" is refined: the summary from Level 1 -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
