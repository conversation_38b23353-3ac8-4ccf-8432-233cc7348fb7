#!/usr/bin/env python3
"""
Batch Execution System for AI Systems
Allows concurrent execution of multiple sequences through JSON configuration
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator

from main import execute_sequence, ExecutorConfig, TemplateCatalog, SequenceManager, Config
from config import EXECUTION_DEFAULTS


# =============================================================================
# BATCH CONFIGURATION MODELS
# =============================================================================

class BatchJob(BaseModel):
    """Configuration for a single batch job."""
    name: str = Field(description="Human-readable name for this job")
    prompt: str = Field(description="User prompt for this job")
    sequence: str = Field(description="Sequence specification (e.g., '1000', '4200|1200|3400|3300')")
    models: List[str] = Field(description="List of models to use")
    
    # Optional execution parameters
    chain_mode: Optional[bool] = Field(default=None, description="Enable/disable chain mode")
    aggregator: Optional[str] = Field(default=None, description="Aggregator sequence specification")
    aggregator_inputs: Optional[List[str]] = Field(default=None, description="Aggregator input step IDs")
    temperature: Optional[float] = Field(default=None, description="Model temperature")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens")
    
    # Output configuration
    output_prefix: Optional[str] = Field(default=None, description="Custom output file prefix")
    
    @validator('models')
    def validate_models(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one model must be specified")
        return v


class BatchConfig(BaseModel):
    """Complete batch execution configuration."""
    version: str = Field(default="1.0", description="Batch config version")
    name: str = Field(description="Batch execution name")
    description: Optional[str] = Field(default=None, description="Batch description")
    
    # Global defaults
    defaults: Dict[str, Any] = Field(default_factory=dict, description="Default execution parameters")
    
    # Jobs to execute
    jobs: List[BatchJob] = Field(description="List of jobs to execute")
    
    # Execution options
    concurrent: bool = Field(default=True, description="Run jobs concurrently")
    max_concurrent: Optional[int] = Field(default=None, description="Maximum concurrent jobs")
    
    @validator('jobs')
    def validate_jobs(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one job must be specified")
        
        # Check for duplicate job names
        names = [job.name for job in v]
        if len(names) != len(set(names)):
            raise ValueError("Job names must be unique")
        
        return v


# =============================================================================
# BATCH EXECUTOR
# =============================================================================

class BatchExecutor:
    """Executes multiple sequences concurrently based on JSON configuration."""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.batch_config: Optional[BatchConfig] = None
        self.catalog = None
        self.results: Dict[str, Any] = {}
        
    def load_config(self) -> BatchConfig:
        """Load and validate batch configuration from JSON file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Batch config file not found: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.batch_config = BatchConfig(**config_data)
            return self.batch_config
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in batch config: {e}")
        except Exception as e:
            raise ValueError(f"Invalid batch configuration: {e}")
    
    def load_catalog(self):
        """Load template catalog."""
        self.catalog = TemplateCatalog.load_catalog()
        if not self.catalog:
            raise RuntimeError("Failed to load template catalog")
    
    async def execute_job(self, job: BatchJob, job_index: int) -> Dict[str, Any]:
        """Execute a single batch job."""
        print(f"\n🚀 Starting job '{job.name}' ({job_index + 1}/{len(self.batch_config.jobs)})")
        
        try:
            # Resolve sequence steps
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, job.sequence
            )
            
            if not sequence_steps:
                raise ValueError(f"Sequence '{job.sequence}' not found")
            
            # Apply defaults and job-specific settings
            defaults = self.batch_config.defaults
            
            # Generate output filename
            timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")
            if job.output_prefix:
                output_filename = f"{job.output_prefix}--{timestamp}--{job.name.replace(' ', '_')}.json"
            else:
                output_filename = f"batch--{timestamp}--{job.name.replace(' ', '_')}.json"
            
            output_path = Path("src/output") / output_filename
            
            # Create executor configuration
            config = ExecutorConfig(
                sequence_steps=sequence_steps,
                user_prompt=job.prompt,
                sequence_id=job.sequence,
                models=job.models,
                output_file=str(output_path),
                system_instruction_extractor=TemplateCatalog.get_system_instruction,
                
                # Execution parameters (job overrides defaults)
                chain_mode=job.chain_mode if job.chain_mode is not None else defaults.get('chain_mode', EXECUTION_DEFAULTS['chain_mode']),
                show_inputs=defaults.get('show_inputs', EXECUTION_DEFAULTS['show_inputs']),
                show_system_instructions=defaults.get('show_system_instructions', EXECUTION_DEFAULTS['show_system_instructions']),
                show_responses=defaults.get('show_responses', EXECUTION_DEFAULTS['show_responses']),
                minified_output=defaults.get('minified_output', EXECUTION_DEFAULTS['minified_output']),
                
                # Optional parameters
                aggregator=job.aggregator,
                aggregator_inputs=job.aggregator_inputs,
                temperature=job.temperature if job.temperature is not None else defaults.get('temperature'),
                max_tokens=job.max_tokens if job.max_tokens is not None else defaults.get('max_tokens')
            )
            
            # Execute the sequence
            results = await execute_sequence(config=config)
            
            print(f"✅ Completed job '{job.name}'")
            print(f"📁 Output saved to: {output_path}")
            
            return {
                'job_name': job.name,
                'status': 'success',
                'output_file': str(output_path),
                'sequence': job.sequence,
                'models': job.models,
                'steps_executed': len(results) if results else 0
            }
            
        except Exception as e:
            print(f"❌ Failed job '{job.name}': {e}")
            return {
                'job_name': job.name,
                'status': 'failed',
                'error': str(e),
                'sequence': job.sequence,
                'models': job.models
            }
    
    async def execute_batch(self) -> Dict[str, Any]:
        """Execute all jobs in the batch configuration."""
        if not self.batch_config:
            raise RuntimeError("Batch configuration not loaded")
        
        print(f"\n🎯 Starting batch execution: {self.batch_config.name}")
        if self.batch_config.description:
            print(f"📝 Description: {self.batch_config.description}")
        
        print(f"📊 Total jobs: {len(self.batch_config.jobs)}")
        print(f"⚡ Concurrent execution: {'Yes' if self.batch_config.concurrent else 'No'}")
        
        if self.batch_config.max_concurrent:
            print(f"🔢 Max concurrent: {self.batch_config.max_concurrent}")
        
        start_time = datetime.now()
        
        if self.batch_config.concurrent:
            # Execute jobs concurrently
            if self.batch_config.max_concurrent:
                # Use semaphore to limit concurrency
                semaphore = asyncio.Semaphore(self.batch_config.max_concurrent)
                
                async def execute_with_semaphore(job, index):
                    async with semaphore:
                        return await self.execute_job(job, index)
                
                tasks = [
                    execute_with_semaphore(job, i) 
                    for i, job in enumerate(self.batch_config.jobs)
                ]
            else:
                # No limit on concurrency
                tasks = [
                    self.execute_job(job, i) 
                    for i, job in enumerate(self.batch_config.jobs)
                ]
            
            job_results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # Execute jobs sequentially
            job_results = []
            for i, job in enumerate(self.batch_config.jobs):
                result = await self.execute_job(job, i)
                job_results.append(result)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Process results
        successful_jobs = [r for r in job_results if isinstance(r, dict) and r.get('status') == 'success']
        failed_jobs = [r for r in job_results if isinstance(r, dict) and r.get('status') == 'failed']
        exception_jobs = [r for r in job_results if isinstance(r, Exception)]
        
        # Summary
        print(f"\n📈 Batch execution completed!")
        print(f"⏱️  Total duration: {duration}")
        print(f"✅ Successful jobs: {len(successful_jobs)}")
        print(f"❌ Failed jobs: {len(failed_jobs) + len(exception_jobs)}")
        
        if failed_jobs:
            print(f"\n❌ Failed jobs:")
            for job in failed_jobs:
                print(f"  - {job['job_name']}: {job['error']}")
        
        if exception_jobs:
            print(f"\n💥 Jobs with exceptions:")
            for i, exc in enumerate(exception_jobs):
                print(f"  - Job {i}: {exc}")
        
        return {
            'batch_name': self.batch_config.name,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'total_jobs': len(self.batch_config.jobs),
            'successful_jobs': len(successful_jobs),
            'failed_jobs': len(failed_jobs) + len(exception_jobs),
            'job_results': job_results
        }


# =============================================================================
# CLI INTERFACE
# =============================================================================

async def main():
    """CLI entry point for batch execution."""
    if len(sys.argv) != 2:
        print("Usage: python src/batch_executor.py <batch_config.json>")
        sys.exit(1)
    
    config_path = sys.argv[1]
    
    try:
        # Initialize and configure
        Config.configure_litellm()
        
        # Create and run batch executor
        executor = BatchExecutor(config_path)
        executor.load_config()
        executor.load_catalog()
        
        # Execute batch
        results = await executor.execute_batch()
        
        # Save batch results
        timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")
        results_file = Path("src/output") / f"batch_results--{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 Batch results saved to: {results_file}")
        
    except Exception as e:
        print(f"❌ Batch execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
