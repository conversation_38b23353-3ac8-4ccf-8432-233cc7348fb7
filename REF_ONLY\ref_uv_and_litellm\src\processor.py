#!/usr/bin/env python3

#===================================================================
# IMPORTS
#===================================================================
import os
import re
import json
import glob
import sys
import datetime


#===================================================================
# BASE CONFIG
#===================================================================
class TemplateConfig:
    LEVEL = None
    FORMAT = None
    SOURCE_DIR = None

    # Series organization based on step patterns (X000)
    SERIES = {
        0: {"description": "Special instructions", "pattern": "a", "steps": "Single step (a)"},
        1000: {"description": "Single-step instructions", "pattern": "a", "steps": "Single step (a)"},
        2000: {"description": "Double-step instructions", "pattern": "a-b", "steps": "Two steps (a-b)"},
        3000: {"description": "Triple-step instructions", "pattern": "a-c", "steps": "Three steps (a-c)"},
        4000: {"description": "Quad-step instructions", "pattern": "a-d", "steps": "Four steps (a-d)"},
        5000: {"description": "Five-step instructions", "pattern": "a-e", "steps": "Five steps (a-e)"},
        6000: {"description": "Six-step instructions", "pattern": "a-f", "steps": "Six steps (a-f)"},
        7000: {"description": "Seven-step instructions", "pattern": "a-g", "steps": "Seven steps (a-g)"},
        8000: {"description": "Eight-step instructions", "pattern": "a-h", "steps": "Eight steps (a-h)"},
        9000: {"description": "Nine-step instructions", "pattern": "a-i", "steps": "Nine steps (a-i)"}
    }

    # Subseries organization based on template type (0X00)
    SUBSERIES = {
        0: {"name": "rephrasers", "description": "Instruction rephrasing and conversion"},
        100: {"name": "expanders", "description": "Content expansion and elaboration"},
        200: {"name": "clarifiers", "description": "Clarity enhancement and insight extraction"},
        300: {"name": "enhancers", "description": "Instruction enhancement and improvement"},
        400: {"name": "compressors", "description": "Content compression and distillation"},
        500: {"name": "reserved", "description": "Reserved for future use"},
        600: {"name": "reserved", "description": "Reserved for future use"},
        700: {"name": "generators", "description": "Template and content generation"},
        800: {"name": "reserved", "description": "Reserved for future use"},
        900: {"name": "critics", "description": "Critical analysis and evaluation"}
    }

    # Sequence definition
    SEQUENCE = {
        "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
        "id_group": 1,
        "step_group": 2,
        "name_group": 3,
        "order_function": lambda step: ord(step) - ord('a')
    }

    @classmethod
    def get_series_for_id(cls, template_id):
        """Determine which number series a template ID belongs to."""
        try:
            id_num = int(template_id.split('-')[0])
            series_base = (id_num // 1000) * 1000
            if series_base in cls.SERIES:
                return f"{series_base}-series", cls.SERIES[series_base]
            return "unknown-series", {"description": "Unknown series"}
        except (ValueError, IndexError):
            return "unknown-series", {"description": "Unknown series"}



    # Content extraction patterns
    PATTERNS = {}

    # Path helpers
    @classmethod
    def get_output_filename(cls):
        if not cls.FORMAT or not cls.LEVEL:
            raise NotImplementedError("FORMAT/LEVEL required.")
        return f"{cls.LEVEL}.{cls.FORMAT}.templates.json"

    @classmethod
    def get_full_output_path(cls, script_dir):
        # Output to generated/ directory within src/
        generated_dir = os.path.join(script_dir, "generated")
        os.makedirs(generated_dir, exist_ok=True)
        return os.path.join(generated_dir, cls.get_output_filename())

    @classmethod
    def get_full_source_path(cls, script_dir):
        # For .md files, look in generated/ directory within src/
        if cls.FORMAT == "md":
            md_dir = os.path.join(script_dir, "generated")
            if hasattr(cls, 'SOURCE_DIRS'):
                return [os.path.join(md_dir, source_dir)
                        for source_dir in cls.SOURCE_DIRS]
            else:
                return [md_dir]
        else:
            # For .py files, look in templates/ directory
            templates_dir = os.path.join(script_dir, "templates")
            if hasattr(cls, 'SOURCE_DIRS'):
                return [os.path.join(templates_dir, source_dir)
                        for source_dir in cls.SOURCE_DIRS]
            elif hasattr(cls, 'SOURCE_DIR'):
                return [os.path.join(templates_dir, cls.SOURCE_DIR)]
            else:
                return [templates_dir]


#===================================================================
# FORMAT: MARKDOWN (lvl1)
#===================================================================
class TemplateConfigMD(TemplateConfig):
    LEVEL = "lvl1"
    FORMAT = "md"
    SOURCE_DIRS = ["."]  # Look in generated/ directory for .md files

    # Keywords to extract from interpretation text
    KEYWORDS_TO_MATCH = [
        # function
        "amplifier",
        "analyze",
        "architect",
        "assessor",
        "compressor",
        "creator",
        "distiller",
        "enhancer",
        "evaluator",
        "expander",
        "extractor",
        "finalizer",
        "formalizer",
        "generator",
        "innovator",
        "intensifier",
        "matcher",
        "optimizer",
        "orchestrator",
        "refiner",
        "rephraser",
        "transformer",
        "translator",
        "validator",
        # actions
        "accelerate",
        "aggregate",
        "amplify",
        "arrange",
        "articulate",
        "assess",
        "augment",
        "categorize",
        "clarify",
        "combine",
        "compare",
        "compress",
        "concretize",
        "condense",
        "converge",
        "convey",
        "critique",
        "crystallize",
        "decompose",
        "distill",
        "elaborate",
        "electrify",
        "emphasize",
        "enforce",
        "enhance",
        "evaluate",
        "expand",
        "explain",
        "extract",
        "finalization",
        "finalize",
        "formalize",
        "generate",
        "harmonize",
        "highlight",
        "identify",
        "implement",
        "incorporate",
        "initialize",
        "innovate",
        "integrate",
        "intensify",
        "interpret",
        "maximize",
        "optimize",
        "orchestrate",
        "organize",
        "parse",
        "pinpoint",
        "planning",
        "polish",
        "predict",
        "present",
        "preserve",
        "prioritize",
        "propose",
        "recognize",
        "reduce",
        "remap",
        "reorder",
        "rephrase",
        "solve",
        "standardize",
        "strategize",
        "strategy",
        "summarize",
        "transform",
        "translate",
        "validate",
        "visualize",
        # characteristics
        "ability",
        "abstract",
        "actionable",
        "adaptive",
        "aesthetics",
        "agent",
        "analysis",
        "animation",
        "application",
        "architecture",
        "assistant",
        "atomic",
        "authentic",
        "bidirectional",
        "brilliance",
        "brilliant",
        "camera",
        "candidate",
        "chain",
        "characters",
        "checklist",
        "cinematic",
        "code",
        "cognitive",
        "cohesive",
        "comment",
        "comments",
        "completely",
        "compression",
        "conceptual",
        "concise",
        "concistent",
        "condensed",
        "config",
        "conformity",
        "connection",
        "constructive",
        "context",
        "creative",
        "critical",
        "curious",
        "deliberate",
        "desired",
        "developer",
        "development",
        "directional",
        "director",
        "dirtree",
        "discernment",
        "distinct",
        "document",
        "documentation",
        "dramatic",
        "dynamic",
        "effective",
        "emotional",
        "encapsulate",
        "environment",
        "essential",
        "exceptional",
        "expert",
        "extraordinary",
        "extremely",
        "factor",
        "feature",
        "figure",
        "flexibility",
        "focus",
        "folder",
        "foundational",
        "frequency",
        "full-stack",
        "function",
        "functional",
        "fundamental",
        "generalized",
        "genuine",
        "gui",
        "harmonics",
        "hierarchical",
        "hierarchy",
        "high-value",
        "holistic",
        "image",
        "impeccably",
        "implication",
        "implicit",
        "important",
        "inherent",
        "input",
        "insights",
        "instruction",
        "integral",
        "integrity",
        "intelligence",
        "interface",
        "intuitive",
        "justification",
        "knowledge",
        "layered",
        "lighting",
        "limitation",
        "linear",
        "mathematical",
        "maximal",
        "maximally",
        "maximum",
        "merge",
        "meta",
        "metaphor",
        "meticulously",
        "minimal",
        "minimum",
        "momentum",
        "movement",
        "navigation",
        "necessary",
        "objectively",
        "optimal",
        "original",
        "orthogonal",
        "output",
        "package",
        "pattern",
        "perpetual",
        "personality",
        "perspective",
        "philosophical",
        "philosophy",
        "pipeline",
        "plugin",
        "plugins",
        "poetic",
        "powerful",
        "primary",
        "principle",
        "process",
        "profound",
        "project",
        "prompt",
        "question",
        "readme",
        "recursive",
        "redundant",
        "refactor",
        "replace",
        "representation",
        "resonate",
        "roadmap",
        "routing",
        "rules",
        "scene",
        "schema",
        "script",
        "scripts",
        "seamlessly",
        "sentence",
        "sequence",
        "sequential",
        "significance",
        "significant",
        "solution",
        "structural",
        "styling",
        "succinct",
        "summary",
        "superior",
        "supreme",
        "surgical",
        "systemic",
        "tailwind",
        "tangible",
        "team",
        "technique",
        "template",
        "test",
        "title",
        "tldr",
        "tone",
        "tools",
        "totally",
        "trajectory",
        "typescript",
        "ui",
        "ultimately",
        "ultra",
        "unambiguous",
        "underlying",
        "understandable",
        "unified",
        "unique",
        "unnecessary",
        "usage",
        "utility",
        "ux",
        "valuable",
        "video",
        "visual",
        # concepts
        "absolute",
        "accuracy",
        "align",
        "automation",
        "blueprint",
        "brevity",
        "clarity",
        "codestyle",
        "coherence",
        "cohesion",
        "complexity",
        "comprehensibility",
        "concept",
        "constraint",
        "curiosity",
        "directive",
        "docstring",
        "docstrings",
        "easily",
        "efficiency",
        "elegance",
        "essence",
        "excellence",
        "goal",
        "impact",
        "imperative",
        "insight",
        "intensity",
        "intent",
        "interpretation",
        "leverage",
        "lineage",
        "maintainability",
        "mandate",
        "mastery",
        "meaning",
        "minimalism",
        "myself",
        "perfection",
        "performance",
        "potency",
        "precision",
        "procedural",
        "purpose",
        "readability",
        "redundancy",
        "relationship",
        "resonance",
        "shortcomings",
        "simplicity",
        "software",
        "structure",
        "synthesis",
        "systematic",
        "transformation",
        "understanding",
        "value",
        "workflow",
        # languages
        "english",
        "language",
        "norwegian",
        # software
        "ahk",
        "anthropic",
        "batch",
        "blender",
        "cmd",
        "css",
        "cursor",
        "gemini",
        "html",
        "json",
        "llm",
        "markdown",
        "mcp",
        "nextjs",
        "openai",
        "perplexity",
        "python",
        "react",
        "runway",
        "shell",
        "sublime",
        "system_message",
        "vscode",
        "xml",
        # ringerikelandskap
        "ringerike",
        # kuci
        "curiouslyinept",
        "kuci",
        # meta
        "healthcare",
    ]

    # Combined pattern for lvl1 markdown templates
    _LVL1_MD_PATTERN = re.compile(
        r"\[(.*?)\]"     # Group 1: Title
        r"\s*"           # Match (but don't capture) whitespace AFTER title
        r"(.*?)"         # Group 2: Capture Interpretation text
        r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
        r"(`\{.*?\}`)"   # Group 3: Transformation
    )

    # Pattern for context field - improved to handle nested JSON properly
    _CONTEXT_PATTERN = re.compile(r"Context:\s*(\{.*)", re.DOTALL)

    @staticmethod
    def _extract_json_context(text):
        """Extract complete JSON object from context text."""
        if not text or not text.strip().startswith('{'):
            return None

        # Find the complete JSON object by counting braces
        brace_count = 0
        start_pos = text.find('{')
        if start_pos == -1:
            return None

        for i, char in enumerate(text[start_pos:], start_pos):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    # Found the end of the JSON object
                    json_str = text[start_pos:i+1]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        return None
        return None

    PATTERNS = {
        "title": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(1).strip() if m else ""
        },
        "interpretation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(2).strip() if m else ""
        },
        "transformation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(3).strip() if m else ""
        },
        "context": {
            "pattern": _CONTEXT_PATTERN,
            "default": None,
            "extract": lambda m: TemplateConfigMD._extract_json_context(m.group(1)) if m else None
        }
    }


#===================================================================
# HELPERS
#===================================================================
def _extract_field(content, pattern_cfg):
    try:
        match = pattern_cfg["pattern"].search(content)
        return pattern_cfg["extract"](match)
    except Exception:
        return pattern_cfg.get("default", "")


def extract_keywords(text, keywords):
    """Extract keywords from text and return pipe-delimited string."""
    if not text or not keywords:
        return ""

    matches = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword.lower() in text_lower:
            matches.append(keyword)

    return "|".join(matches) if matches else ""


def _is_extraction_failed(parts):
    """Check if the extraction failed and we should use fallback."""
    title = parts.get("title", "").strip()
    interpretation = parts.get("interpretation", "").strip()
    transformation = parts.get("transformation", "").strip()

    # Check for generic/placeholder content that indicates failed extraction
    generic_indicators = ["Title", "Interpretation", "Transformation", "Execute as:", "`{Transformation}`"]

    # Failed if any part is empty
    if not title or not interpretation or not transformation:
        return True

    # Failed if any part is just a generic placeholder
    if any(part.strip() in generic_indicators for part in [title, interpretation, transformation]):
        return True

    # Failed if interpretation is too short (likely not the real content)
    if len(interpretation) < 50:
        return True

    return False


def _create_fallback_parts(content, template_id):
    """Create fallback parts when regex extraction fails."""
    # Try to extract title from first line if it has brackets
    lines = content.split('\n')
    first_line = lines[0].strip() if lines else ""

    # Extract title from [Title] pattern if present
    title_match = re.match(r'\[(.*?)\]', first_line)
    if title_match:
        title = title_match.group(1).strip()
        # Use the rest of the content as interpretation
        remaining_content = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
    else:
        # Use template_id as title and full content as interpretation
        title = template_id.replace('-', ' ').replace('_', ' ').title()
        remaining_content = content

    # Leave transformation empty in fallback mode
    transformation = ""

    return {
        "title": title,
        "interpretation": remaining_content,
        "transformation": transformation
    }


def extract_metadata(content, template_id, config):
    content = content.strip()
    parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}

    # Check if extraction failed and use fallback if needed
    if _is_extraction_failed(parts):
        print(f"INFO: Using fallback extraction for {template_id}")
        parts = _create_fallback_parts(content, template_id)

    # Extract keywords from interpretation text
    if hasattr(config, 'KEYWORDS_TO_MATCH'):
        interpretation = parts.get("interpretation", "")
        if interpretation:
            keywords = extract_keywords(interpretation, config.KEYWORDS_TO_MATCH)
            parts["keywords"] = keywords

    return {"raw": content, "parts": parts}


#===================================================================
# CATALOG GENERATION
#===================================================================
def generate_catalog(config, script_dir):
    # Find templates and extract metadata from all source directories
    source_paths = config.get_full_source_path(script_dir)
    template_files = []

    print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")

    # Collect files from all source directories
    for source_path in source_paths:
        if os.path.exists(source_path):
            files_in_dir = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))
            template_files.extend(files_in_dir)
            print(f"Source: {source_path} (*.{config.FORMAT}) - Found {len(files_in_dir)} files")
        else:
            print(f"Source: {source_path} - Directory not found, skipping")

    print(f"Total found: {len(template_files)} template files across all stages.")

    templates = {}
    sequences = {}

    # Process each template file
    for file_path in template_files:
        filename = os.path.basename(file_path)
        template_id = os.path.splitext(filename)[0]

        try:
            # Read content with proper UTF-8 encoding
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()

            # Extract and store template metadata
            template_data = extract_metadata(content, template_id, config)
            templates[template_id] = template_data

            # Process sequence information from filename
            seq_match = config.SEQUENCE["pattern"].match(template_id)
            if seq_match:
                seq_id = seq_match.group(config.SEQUENCE["id_group"])
                step = seq_match.group(config.SEQUENCE["step_group"])
                seq_order = config.SEQUENCE["order_function"](step)
                sequences.setdefault(seq_id, []).append({
                    "template_id": template_id, "step": step, "order": seq_order
                })
            print(f"SUCCESS: Processed {template_id}")
        except Exception as e:
            print(f"ERROR: {template_id} -> {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

    # Sort sequence steps
    for seq_id, steps in sequences.items():
        try:
            steps.sort(key=lambda step: step["order"])
        except Exception as e:
            print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

    # Analyze series distribution
    series_distribution = {}
    for template_id in templates.keys():
        series_name, series_info = config.get_series_for_id(template_id)
        if series_name not in series_distribution:
            series_distribution[series_name] = {
                "count": 0,
                "description": series_info["description"],
                "templates": []
            }
        series_distribution[series_name]["count"] += 1
        series_distribution[series_name]["templates"].append(template_id)

    # Create catalog with metadata including stage analysis
    timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")

    # Determine source directories info
    if hasattr(config, 'SOURCE_DIRS'):
        source_info = config.SOURCE_DIRS
    else:
        source_info = config.SOURCE_DIR

    return {
        "catalog_meta": {
            "level": config.LEVEL,
            "format": config.FORMAT,
            "generated_at": timestamp,
            "source_directories": source_info,
            "total_templates": len(templates),
            "total_sequences": len(sequences),
            "series_distribution": series_distribution
        },
        "templates": templates,
        "sequences": sequences
    }


def save_catalog(catalog_data, config, script_dir):
    output_path = config.get_full_output_path(script_dir)
    print(f"Output: {output_path}")

    try:
        with open(output_path, 'w', encoding='utf-8', errors='replace') as f:
            json.dump(catalog_data, f, indent=2, ensure_ascii=False)
            total_templates = catalog_data['catalog_meta']['total_templates']
            print(f"SUCCESS: Saved catalog with {total_templates} templates.")
            print("--- Catalog Generation Complete ---")
        return output_path
    except Exception as e:
        print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
        return None


#===================================================================
# STAGE MANAGEMENT UTILITIES
#===================================================================
def print_series_distribution(catalog):
    """Print series distribution analysis."""
    if "series_distribution" not in catalog.get("catalog_meta", {}):
        print("No series distribution data available.")
        return

    print("\n=== Series Distribution Analysis ===")
    series_dist = catalog["catalog_meta"]["series_distribution"]

    for series_name in sorted(series_dist.keys()):
        series_info = series_dist[series_name]
        print(f"\n{series_name.upper()}: {series_info['description']}")
        print(f"  Range: {series_info['range'][0]}-{series_info['range'][1]}")
        print(f"  Templates: {series_info['count']}")

        if series_info['templates']:
            # Group by category for better display
            templates_by_category = {}
            for template_id in sorted(series_info['templates']):
                category = template_id.split('-')[-1].split('_')[0] if '_' in template_id else 'misc'
                if category not in templates_by_category:
                    templates_by_category[category] = []
                templates_by_category[category].append(template_id)

            for category, template_ids in sorted(templates_by_category.items()):
                print(f"    {category}: {', '.join(template_ids)}")



def validate_template_compliance(catalog):
    """Validate that templates have required components."""
    issues = []
    templates = catalog.get("templates", {})

    for template_id, template_data in templates.items():
        # Check for templates that might need validation
        parts = template_data.get("parts", {})
        if not all(key in parts for key in ["title", "interpretation", "transformation"]):
            issues.append(f"Template {template_id} missing required parts: {[k for k in ['title', 'interpretation', 'transformation'] if k not in parts]}")

    return issues


def show_series_overview(catalog):
    """Display comprehensive series overview."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass

    print("=" * 60)
    print("SERIES-BASED TEMPLATE ORGANIZATION SYSTEM")
    print("=" * 60)

    print("\nSERIES DEFINITIONS:")
    for series_base, series_info in TemplateConfig.SERIES.items():
        start = series_base
        end = series_base + 999
        print(f"  {series_base:4d}-{end:4d}: {series_info['description']}")

    print_stage_distribution(catalog)


def print_stage_distribution(catalog):
    """Print distribution of templates by series."""
    print("\nSERIES DISTRIBUTION:")

    series_distribution = catalog.get("catalog_meta", {}).get("series_distribution", {})
    if not series_distribution:
        print("  No series distribution data available.")
        return

    for series_key in sorted(series_distribution.keys()):
        series_data = series_distribution[series_key]
        if isinstance(series_data, dict):
            count = series_data.get("count", 0)
            description = series_data.get("description", "Unknown series")
            print(f"  {series_key:15s}: {count:3d} templates ({description})")
        else:
            # Fallback for simple count format
            count = series_data
            print(f"  {series_key:15s}: {count:3d} templates")


def show_template_improvements(catalog):
    """Suggest templates that could be improved."""
    print("\nTEMPLATE IMPROVEMENT SUGGESTIONS:")

    templates = catalog.get("templates", {})
    suggestions = []

    # Find templates that might need improvement
    incomplete_templates = []
    for template_id, template_data in templates.items():
        parts = template_data.get("parts", {})
        if not all(key in parts for key in ["title", "interpretation", "transformation"]):
            incomplete_templates.append(template_id)

    if incomplete_templates:
        suggestions.append(f"   REVIEW: {len(incomplete_templates)} templates missing required parts")
        for tid in incomplete_templates[:3]:  # Show first 3
            suggestions.append(f"     • {tid}")

    if suggestions:
        for suggestion in suggestions:
            print(suggestion)
    else:
        print("   SUCCESS: All templates have required components")


def demonstrate_workflow():
    """Demonstrate the complete series-based workflow."""
    print("\n" + "=" * 60)
    print("SERIES-BASED WORKFLOW DEMONSTRATION")
    print("=" * 60)

    print("\n1️⃣  REPHRASERS WORKFLOW (1000-series):")
    print("   • Create template with numeric ID (e.g., 1000-a-template_name)")
    print("   • Test and iterate in 1000-1999 range")
    print("   • Single-step instruction patterns")

    print("\n2️⃣  EXPANDERS WORKFLOW (1100-series):")
    print("   • Templates that expand and elaborate content")
    print("   • Focus on adding detail and context")
    print("   • Manual ID assignment for organization")

    print("\n3️⃣  COMPRESSORS WORKFLOW (1200-series):")
    print("   • Templates that condense and summarize")
    print("   • Focus on extracting key information")
    print("   • Stable and tested for real-world usage")

    print("\n4️⃣  ENHANCERS WORKFLOW (1400-series):")
    print("   • Templates that improve and refine content")
    print("   • Focus on quality enhancement")
    print("   • Advanced template sequences")


#===================================================================
# GENERATOR BASE CLASS
#===================================================================
class BaseGenerator:
    """Base class for template generators to eliminate code duplication."""

    def __init__(self, series_base=None, output_dir=None):
        from pathlib import Path
        import os
        self.series_base = series_base  # Will be auto-detected if None
        self.series_config = None  # Will be set after series detection
        self.series_range = None  # Will be set after series detection
        self.output_dir = Path(output_dir or os.getenv("AI_OUTPUT_DIR") or Path(__file__).parent / "generated")





    def create_template_files(self, templates):
        """Generate markdown template files."""
        self.output_dir.mkdir(exist_ok=True)
        created_files = []

        for template_key, template in templates.items():
            filename = template_key
            filepath = self.output_dir / f"{filename}.md"

            # Build content with optional test and context fields
            content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
            if 'context' in template:
                import json
                context_json = json.dumps(template['context'], indent=2, ensure_ascii=False)
                content += f"\n\nContext: {context_json}"
            if 'test' in template:
                content += f"\n\nTest: {template['test']}"

            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)

            created_files.append(f"{filename}.md")

        return created_files

    def _detect_series_from_templates(self, templates):
        """Detect series and subseries from template IDs."""
        if not templates:
            return 1000, 0  # Default fallback

        # Extract numeric IDs from template keys
        template_ids = []
        for key in templates.keys():
            # Extract number from keys like "1000-a-instruction_converter"
            parts = key.split('-')
            if parts and parts[0].isdigit():
                template_ids.append(int(parts[0]))

        if not template_ids:
            return 1000, 0  # Default fallback

        # Determine series and subseries from the first template ID
        first_id = template_ids[0]
        series_base = (first_id // 1000) * 1000
        subseries_base = ((first_id % 1000) // 100) * 100
        return series_base, subseries_base

    def run(self, templates):
        """Main execution function."""
        import sys

        # Ensure UTF-8 output encoding
        if hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8')
            except:
                pass

        # Auto-detect series and subseries if not set
        if self.series_base is None:
            self.series_base, subseries_base = self._detect_series_from_templates(templates)
        else:
            # If series_base is set, still detect subseries
            _, subseries_base = self._detect_series_from_templates(templates)

        # Set series configuration
        self.series_config = TemplateConfig.SERIES.get(self.series_base, {"description": "Unknown series"})
        self.series_range = (self.series_base, self.series_base + 999)

        # Get subseries configuration
        subseries_config = TemplateConfig.SUBSERIES.get(subseries_base, {"name": "unknown", "description": "Unknown type"})

        # Display series configuration
        range_str = f"{self.series_range[0]}-{self.series_range[1]}"
        print(f"SERIES: {self.series_base} ({range_str})")
        print(f"DESCRIPTION: {self.series_config['description']}")
        print(f"TYPE: {subseries_config['name'].title()} ({subseries_config['description']})")
        print()

        created_files = self.create_template_files(templates)

        print(f"SUCCESS: Created {len(created_files)} templates:")
        for file in created_files:
            print(f"   - {file}")

        print(f"\nLOCATION: Templates generated in: {self.output_dir}")
        series_desc = self.series_config['description']
        print(f"\nSERIES RANGE: {range_str} ({series_desc})")
        pattern = self.series_config.get('pattern', 'a')
        steps_desc = self.series_config.get('steps', 'Single step (a)')
        print(f"SEQUENCE PATTERN: {pattern}")
        print(f"   {steps_desc}")

#===================================================================
# IMPORTABLE API FOR EXTERNAL SCRIPTS
#===================================================================
def get_default_catalog_path(script_dir=None):
    """Get the path to the default catalog file."""
    if script_dir is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
    return TemplateConfigMD().get_full_output_path(script_dir)


def load_catalog(catalog_path=None):
    """Load a catalog from a JSON file."""
    if catalog_path is None:
        catalog_path = get_default_catalog_path()

    if not os.path.exists(catalog_path):
        raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")


def get_template(catalog, template_id):
    """Get a template by its ID."""
    if "templates" not in catalog or template_id not in catalog["templates"]:
        return None
    return catalog["templates"][template_id]


def get_sequence(catalog, sequence_id):
    """Get all templates in a sequence, ordered by steps."""
    if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
        return []

    sequence_steps = catalog["sequences"][sequence_id]
    return [(step["step"], get_template(catalog, step["template_id"]))
            for step in sequence_steps]


def get_all_sequences(catalog):
    """Get a list of all sequence IDs."""
    if "sequences" not in catalog:
        return []
    return list(catalog["sequences"].keys())


def get_system_instruction(template):
    """Convert a template to a system instruction format."""
    if not template or "parts" not in template:
        return None

    parts = template["parts"]
    instruction = f"# {parts.get('title', '')}\n\n"

    if "interpretation" in parts and parts["interpretation"]:
        instruction += f"{parts['interpretation']}\n\n"

    if "transformation" in parts and parts["transformation"]:
        instruction += parts["transformation"]

    if "context" in parts and parts["context"]:
        instruction += f"\n\n## Context Data\n\n"
        context_json = json.dumps(parts["context"], indent=2, ensure_ascii=False)
        instruction += f"```json\n{context_json}\n```"

    return instruction


def regenerate_catalog(force=False):
    """Regenerate the catalog file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config = TemplateConfigMD()
    catalog_path = config.get_full_output_path(script_dir)

    if os.path.exists(catalog_path) and not force:
        # Check if catalog is older than any template file
        catalog_mtime = os.path.getmtime(catalog_path)
        templates_dirs = config.get_full_source_path(script_dir)

        needs_update = False
        for templates_dir in templates_dirs:
            if os.path.exists(templates_dir):
                for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                    if os.path.getmtime(file_path) > catalog_mtime:
                        needs_update = True
                        break
                if needs_update:
                    break

        if not needs_update:
            # print("Catalog is up to date")
            return load_catalog(catalog_path)

    # Generate and save new catalog
    catalog = generate_catalog(config, script_dir)
    save_catalog(catalog, config, script_dir)
    return catalog


#===================================================================
# SCRIPT EXECUTION
#===================================================================
if __name__ == "__main__":
    import argparse

    SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

    parser = argparse.ArgumentParser(description="Generate template catalog with stage-based organization")
    parser.add_argument("--force", action="store_true", help="Force regeneration even if files are up to date")
    parser.add_argument("--show-stages", action="store_true", help="Show stage distribution analysis and exit")

    parser.add_argument("--validate-stages", action="store_true", help="Validate stage compliance and exit")
    parser.add_argument("--stage-overview", action="store_true", help="Show complete stage system overview")
    parser.add_argument("--migration-suggestions", action="store_true", help="Show template migration suggestions")
    parser.add_argument("--workflow-demo", action="store_true", help="Demonstrate stage-based workflow")
    parser.add_argument("--api-test", action="store_true", help="Run API test and list sequences")
    args = parser.parse_args()

    try:
        config_to_use = TemplateConfigMD
        active_config = config_to_use()

        # Load existing catalog for analysis commands
        analysis_commands = [args.show_stages, args.validate_stages,
                           args.stage_overview, args.migration_suggestions, args.workflow_demo, args.api_test]
        if any(analysis_commands):
            catalog = regenerate_catalog(force=False)

            if args.show_stages:
                print_stage_distribution(catalog)
                sys.exit(0)



            if args.validate_stages:
                print("STAGE COMPLIANCE VALIDATION:")
                issues = validate_stage_compliance(catalog)
                if issues:
                    print(f"   WARNING: Found {len(issues)} compliance issues:")
                    for issue in issues:
                        print(f"      - {issue}")
                else:
                    print("   SUCCESS: All templates are stage-compliant")
                sys.exit(0)

            if args.stage_overview:
                show_stage_overview(catalog)
                sys.exit(0)

            if args.migration_suggestions:
                show_migration_suggestions(catalog)
                sys.exit(0)

            if args.workflow_demo:
                demonstrate_workflow()
                sys.exit(0)

            if args.api_test:
                print("\nAvailable sequences:")
                for seq_id in get_all_sequences(catalog):
                    sequence_steps = get_sequence(catalog, seq_id)
                    if sequence_steps and sequence_steps[0][1] and "parts" in sequence_steps[0][1]:
                        first_step_title = sequence_steps[0][1]["parts"]["title"]
                        print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")
                sys.exit(0)

        # Regular catalog generation
        catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
        save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        # Show stage distribution after generation
        print_stage_distribution(catalog)

    except NotImplementedError as e:
        print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
        sys.exit(1)
    except FileNotFoundError as e:
        print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}", file=sys.stderr)
        sys.exit(1)
