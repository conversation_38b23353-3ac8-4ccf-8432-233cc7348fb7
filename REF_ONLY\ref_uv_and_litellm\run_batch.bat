@echo off
setlocal enabledelayedexpansion

:: Check if batch config file is provided
if "%~1"=="" (
    echo Usage: run_batch.bat ^<batch_config.json^>
    echo.
    echo Examples:
    echo   run_batch.bat batch_configs\example_basic.json
    echo   run_batch.bat batch_configs\example_advanced.json
    echo.
    pause
    exit /b 1
)

:: Setup environment using shared utilities
call batch_utils.bat :setup_environment

:: Run batch execution
echo.
echo Running batch execution with config: %1
echo.
uv run python src/main.py --batch-file "%1"

:: Keep window open
echo.
pause
