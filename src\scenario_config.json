{"perspective_mode": "perceiver-perspective", "scenario_type": "You are a prompt engineer and optimization expert, refine the prompt for maximum clarity and brevity, ensuring the message is conveyed effectively and concisely.", "scenario_context": "Enhance prompt for precise clarity; deliver a brief, clear message effectively.", "tone": "neutral", "audience": "developers", "instructions": ["Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.", "Keep the summary concise: no more than one-third the length of the input.", "Detailed description of the scenario. What problem does it solve? What is the context?", "Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding."], "format": {"sections": ["High Level Summary", "Key Details", "Potential Issues and Risks", "Recommended Next Steps"]}, "example_output": {"title": "High-Level Overview of the Prompt Framework", "sections": {"High Level Summary": "This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).", "Key Details": "- Utilizes a JSON configuration for structured instructions.\n- Employs minimalistic XML templates with placeholders for flexibility.\n- Easily adaptable to various scenarios through modular design.", "Recommended Next Steps": "- Refine templates based on user feedback.\n- Incorporate additional scenarios in the `scenario_config.json`.\n- Emphasize brevity and relevance in all documentation."}}, "inputs": {"initial": "initial_input.txt"}, "keywords": "a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality", "placeholders": {"content": "[PASTE_TEXT_HERE]", "keywords": "[KEYWORDS_HERE]", "scenario_type": "[SCENARIO_TYPE]", "scenario_context": "[SCENARIO_CONTEXT]", "tone": "[TONE]", "audience": "[AUDIENCE]"}, "default_parameters": {"model": "gpt-4-0125-preview", "temperature": 0.7, "max_tokens": 800}, "stages": [{"name": "main_summarization", "prompts_sequence": [{"path": "prompts/summarize/level4/a_promptenhancer.xml", "input_key": "initial"}], "quality_check": {"indicators": ["clear", "example", "concise", "relevant"], "min_count": 2, "min_prompts_before_early_termination": 4}, "run_if": "true"}]}