{
	"folders":
	[
		{
			"path": ".",
            "folder_exclude_patterns": [
                "*.egg-info",
                ".backups",
                ".DS_Store",
                ".git",
                ".hg",
                ".idea",
                ".svn",
                ".vscode",
                "__pycache__",
                "build",
                "dist",
                "env",
                "logs",
                "node_modules",
                "venv",
            ],
            "file_exclude_patterns": [
                "*.log",
                "*.pyc",
                "*..sublime-project",
                "*.pyo",
                "*.sublime-workspace",
                "*.swp",
                "*.tmp",
                ".DS_Store",
                // ".gitignore",
            ]
		}
	],
    "build_systems": [
        {
            "name": "llm_framework_xml.sublime-project",
            "cmd": [
                "$project_path\\venv\\Scripts\\python",
                "-u",
                "${file}"
            ],
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "shell": true,
            "working_dir": "${project_path}"
        }
    ]
}
