{"perspective_mode": "perceiver-perspective", "scenario_type": "LLM Instruction Generation", "scenario_context": "Designing and refining instructions for various LLM tasks while ensuring clarity, thoroughness, and adaptability.", "tone": "neutral", "audience": "developers", "instructions": ["Gather all user/system constraints needed for building effective LLM instructions.", "Ensure each resulting instruction is organized, precise, and easy to follow.", "Combine technical and contextual guidance, focusing on clarity.", "Omit unrelated details or excessive commentary to keep instructions concise."], "format": {"sections": ["Objective Statement", "Prerequisites", "Step-by-Step Actions"]}, "example_output": {"title": "Instruction Generator Overview", "sections": {"Objective Statement": "Defines the primary goal or purpose of the instructions.", "Prerequisites": "Highlights dependencies, context, or resources needed.", "Step-by-Step Actions": "Outlines the clear sequence of tasks or directives."}}, "inputs": {"initial": "initial_input.txt"}, "keywords": "instruction generator for LLM usage", "placeholders": {"content": "[PASTE_TEXT_HERE]", "keywords": "[KEYWORDS_HERE]", "scenario_type": "[SCENARIO_TYPE]", "scenario_context": "[SCENARIO_CONTEXT]", "tone": "[TONE]", "audience": "[AUDIENCE]"}, "default_parameters": {"model": "gpt-4-0125-preview", "temperature": 0.7, "max_tokens": 800}, "stages": [{"name": "instruction_generation", "prompts_sequence": [{"path": "prompts/instruction_generator/level1/gather_requirements.xml", "input_key": "initial"}, {"path": "prompts/instruction_generator/level2/structure_instructions.xml"}, {"path": "prompts/instruction_generator/level3/refine_instructions.xml"}, {"path": "prompts/instruction_generator/level4/finalize_instructions.xml"}], "quality_check": {"indicators": ["clear", "logical", "actionable", "complete"], "min_count": 2, "min_prompts_before_early_termination": 3}, "run_if": "true"}]}