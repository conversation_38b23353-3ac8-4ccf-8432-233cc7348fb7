<prompt>
  <!--
      Purpose (Why?):
      Generate a concise, specialized TL;DR from the final summary.
  -->
  <purpose>Produce a concise "TL;DR" version of the refined text.</purpose>

  <instructions>
    <!-- "How": enforce brevity, specify special constraints -->
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
    <instruction>
      Summarize the text in 2-3 sentences (or fewer than ~60 words), preserving only
      the key takeaways aligned with [SCENARIO_TYPE] and [SCENARIO_CONTEXT].
    </instruction>
    <instruction>
      Output clearly labeled "TL;DR:" so we can distinguish it in logs or final output.
    </instruction>
  </instructions>

  <!--
      Content (What is being refined?):
      The previously generated summary. This is just "[PASTE_TEXT_HERE]" as usual.
  -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
