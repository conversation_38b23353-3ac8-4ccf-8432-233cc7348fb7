<prompt>
  <!--
      Purpose (Why is it being refined?):
      Build upon the extracted key content from Level 1, refining and structuring it within the framework of [SCENARIO_TYPE].
  -->
  <purpose>Refine and organize the summary to align with the [SCENARIO_TYPE] framework.</purpose>

  <!--
      Instructions (How is it being refined?):
      1. Further enhance clarity and structure.
      2. Uphold scenario context: [SCENARIO_CONTEXT], [PERSPECTIVE_INSTRUCTIONS].
      3. Divide the refined summary into a specific format for deeper insights.
  -->
  <instructions>
    <instruction>[PERSPECTIVE_INSTRUCTIONS]</instruction>
    <instruction>See [RECAP] for the current summary and enhance it by refining details and structure.</instruction>
    <instruction>Present the result in Markdown format, with the following sections:
      - High Level Summary
      - Main Points
      - Sentiment
      - 3 Hot Takes (biased toward the author) + 3 Hot Takes (biased against)
    </instruction>
    <instruction>Ensure clarity, coherence, and adherence to scenario goals.</instruction>
    <instruction>Align the output with [SCENARIO_CONTEXT] and use Markdown formatting.</instruction>
  </instructions>

  <recap>
    <!-- Inserted by code: brief recap of Level 1 output -->
  </recap>

  <!--
      Content (What is being refined?):
      The summarized text from Level 1, passed along for further structuring.
  -->
  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>
