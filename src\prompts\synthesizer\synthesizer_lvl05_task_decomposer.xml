<!-- synthesizer_lvl05_task_decomposer.xml -->
<prompt>
  <!-- Purpose (Why): Break down complex parts into smaller, manageable chunks. -->
  <purpose>Decompose complex elements into simpler tasks or components.</purpose>

  <instructions>
    <!-- "How" we refine: identify intricacies and split them out -->
    <instruction>Review the examples generated in the "**Summary of this Step:**" of the [RECAP] from Level 4.</instruction>
    <instruction>Check the [RECAP] from previous levels (1-3) for any intricate or multi-step portions related to the objective, context, or proposed structure.</instruction>
    <instruction>Reorganize or outline these in bite-sized pieces that are easier to tackle, drawing inspiration from the examples.</instruction>
    <instruction>Preserve overall coherence with the main objective and context.</instruction>
    <instruction>Conclude your response with a concise, bulleted summary of the key insights or changes made in this step. Label it "**Summary of this Step:**".</instruction>
  </instructions>

  <recap>
    <!-- Aggregated content from Levels 1–4 -->
  </recap>

  <content>
    [PASTE_TEXT_HERE]
  </content>
</prompt>