  {
  "initial_prompt": "Communicate, using the system itself, a single unified consolidation plan that binds all docs into one authoritative README with exact sections and migration steps; ensure it is actionable and minimal.",
  "sequence_id": "1000|1300",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Communicate, using the system itself, a single unified consolidation plan that binds all docs into one authoritative README with exact sections and migration steps; ensure it is actionable and minimal.",
      "responses": {
          "gpt-5-chat-latest": {
          "model": "gpt-5-chat-latest",
          "content": "{\n  \"instruction_format\": \"Consolidate all documents into one authoritative README by following these steps: \\n\\n1. Gather all existing documents, including their contents and sections. \\n2. Create a new README file to serve as the authoritative document. \\n3. Identify key sections from each document that are essential for migration. \\n4. Organize the README with the following sections: Introduction, Migration Steps, Troubleshooting, FAQ. \\n5. Combine relevant information from each document under the corresponding sections in the README. \\n6. Ensure clarity and conciseness in the migration steps provided. \\n7. Include any specific instructions or warnings that are crucial for migration. \\n8. Review the consolidated README for accuracy and completeness. \\n9. Share the finalized README with relevant stakeholders for feedback and approval. \\n10. Update the README as necessary based on received feedback. \\n\\nThroughout this process, focus on maintaining clarity, brevity, and actionability in the consolidated README.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Enhancer\n\nYour goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:\n\n`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`\n\n## Context Data\n\n```json\n{\n  \"enhancement_principles\": {\n    \"maximal_generality\": \"The instruction must be applicable to the widest possible range of inputs, topics, and contexts.\",\n    \"ruthless_conciseness\": \"Eliminate every superfluous word; every word must serve to increase clarity or impact.\",\n    \"undeniable_actionability\": \"The instruction must be a clear, unambiguous directive that compels specific action.\",\n    \"conceptual_distillation\": \"Boil down complex ideas into their simplest, most potent form.\"\n  },\n  \"success_metrics\": {\n    \"reduced_token_count\": \"A shorter instruction that retains or increases semantic density.\",\n    \"increased_applicability\": \"The instruction is now more widely usable without modification.\",\n    \"sharpened_directive\": \"The instruction's core command is more direct and forceful.\",\n    \"eliminated_ambiguity\": \"No room for misinterpretation of the instruction's purpose.\"\n  }\n}\n```",
      "step": "a",
      "title": "Instruction Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Communicate, using the system itself, a single unified consolidation plan that binds all docs into one authoritative README with exact sections and migration steps; ensure it is actionable and minimal.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Consolidate all documents into one authoritative README by following these steps: \\n\\n1. Gather all existing documents, including their contents and sections. \\n2. Create a new README file to serve as the authoritative document. \\n3. Identify key sections from each document that are essential for migration. \\n4. Organize the README with the following sections: Introduction, Migration Steps, Troubleshooting, FAQ. \\n5. Combine relevant information from each document under the corresponding sections in the README. \\n6. Ensure clarity and conciseness in the migration steps provided. \\n7. Include any specific instructions or warnings that are crucial for migration. \\n8. Review the consolidated README for accuracy and completeness. \\n9. Share the finalized README with relevant stakeholders for feedback and approval. \\n10. Update the README as necessary based on received feedback. \\n\\nThroughout this process, focus on maintaining clarity, brevity, and actionability in the consolidated README.\"\n}",
      "responses": {
          "gpt-5-chat-latest": {
          "model": "gpt-5-chat-latest",
          "content": "{\n  \"enhanced_instruction_format\": \"Consolidate all documents into one authoritative README using these steps: 1. Collect all existing document content. 2. Create a new README as the main document. 3. Select key migration sections from each document. 4. Structure the README with Introduction, Migration Steps, Troubleshooting, FAQ. 5. Merge information under the relevant sections. 6. Ensure concise migration steps. 7. Add essential migration instructions and warnings. 8. Review the README for accuracy. 9. Share with stakeholders for feedback. 10. Update based on input. Prioritize clarity, brevity, and actionability.\" \n}",
        }
      }
    }